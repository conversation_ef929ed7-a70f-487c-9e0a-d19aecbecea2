// Note: getSrc from @livepeer/react/external requires client configuration
// For now, we'll use manual URL construction which is more reliable

// Helper function to check if a URL is a Livepeer playback URL
export function isLivepeerUrl(url: string): boolean {
    return url.includes('livepeer.studio') ||
        url.includes('livepeer.com') ||
        url.includes('lp-playback.studio') ||
        url.includes('livepeercdn.com') ||
        url.includes('.m3u8'); // HLS streams are typically Livepeer
}

// Helper function to extract video ID from Livepeer URL
export function getLivepeerVideoId(url: string): string | null {
    try {
        const urlObj = new URL(url);
        if (urlObj.hostname.includes('livepeer')) {
            // Extract the video ID from the path
            const pathParts = urlObj.pathname.split('/');
            return pathParts[pathParts.length - 1];
        }
        return null;
    } catch {
        return null;
    }
}

// Helper function to extract playback ID from HLS URL
export function extractPlaybackId(url: string): string | null {
    try {
        // Handle different Livepeer URL formats
        if (url.includes('lp-playback.studio')) {
            // Format: https://vod-cdn.lp-playback.studio/raw/957cx7os3h9fyg93/catalyst-vod-com/hls/index.m3u8
            const match = url.match(/\/raw\/([^\/]+)\//);
            return match ? match[1] : null;
        } else if (url.includes('livepeercdn.com')) {
            // Format: https://livepeercdn.com/hls/PLAYBACK_ID/index.m3u8
            const match = url.match(/\/hls\/([^\/]+)\//);
            return match ? match[1] : null;
        } else if (url.includes('livepeer.studio')) {
            // Extract from studio URLs
            const pathParts = url.split('/');
            const hlsIndex = pathParts.indexOf('hls');
            if (hlsIndex !== -1 && pathParts[hlsIndex + 1]) {
                return pathParts[hlsIndex + 1];
            }
        }
        return null;
    } catch (error) {
        console.error('Error extracting playback ID:', error);
        return null;
    }
}

// Helper function to check if CDN is ready
export async function checkCdnReady(url: string): Promise<boolean> {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
    } catch {
        return false;
    }
}

// Convert playback ID or HLS URL to Livepeer React src format
export function getLivepeerSrc(playbackId?: string, hlsUrl?: string) {
    if (hlsUrl) {
        // If we have an HLS URL, create a source object
        console.log('Creating src from HLS URL:', hlsUrl);
        return [{ src: hlsUrl, type: "application/vnd.apple.mpegurl" }];
    } else if (playbackId) {
        console.log('Getting src from playback ID:', playbackId);

        // Use manual construction with multiple CDN formats for better compatibility
        // Priority order: working URLs first (livepeercdn.com is confirmed working)
        const fallbackUrls = [
            `https://livepeercdn.com/hls/${playbackId}/index.m3u8`,
            `https://vod-cdn.lp-playback.studio/raw/${playbackId}/catalyst-vod-com/hls/index.m3u8`,
            `https://livepeer.studio/api/playback/${playbackId}/index.m3u8`
        ];

        console.log('Using CDN URLs:', fallbackUrls);
        return fallbackUrls.map(url => ({ src: url, type: "application/vnd.apple.mpegurl" }));
    }
    return null;
}
