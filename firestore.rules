rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }    // Helper function to check if user owns the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Users collection rules
    match /users/{userId} {
      allow read: if true;  // Allow public read access to user profiles

      // Allow updates to following, following_count, followers_count fields by any authenticated user
      allow update: if isAuthenticated() && (
        isOwner(userId) ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['following', 'following_count', 'followers_count'])
      );

      // Allow other writes only by the owner
      allow create, delete: if isOwner(userId);
      allow list: if true;

      // Nested collections under users
      match /{allChildren=**} {
        allow read: if isAuthenticated();
        allow write: if isOwner(userId);
      }
    }

    // System collections
    match /_connection_test/{docId} {
      allow read, write: if true;  // Allow all operations for connection testing
    }

    match /notifications/{notificationId} {
      allow read: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        resource.data.isPublic == true
      );
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid ||
        request.resource.data.senderId == request.auth.uid
      );
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['status', 'readAt'])
      );
    }

    // Messages collection rules
    match /messages/{messageId} {
      function isParticipant(conversationId) {
        let conversation = get(/databases/$(database)/documents/conversations/$(conversationId));
        return isAuthenticated() &&
          conversation != null &&
          (request.auth.uid in conversation.data.participants ||
           conversation.data.isPublic == true ||
           request.auth.uid == conversation.data.ownerId);
      }      function isValidMessage() {
        let msgData = request.resource.data;
        return msgData.size() > 0 &&
               msgData.conversationId is string &&
               msgData.senderId is string &&
               msgData.senderId == request.auth.uid &&
               msgData.receiverId is string &&
               msgData.content is string &&
               msgData.timestamp is string &&
               msgData.status in ['sent', 'delivered', 'read'] &&
               (!('attachmentType' in msgData) || msgData.attachmentType in ['image', 'file', 'audio', 'video']) &&
               (!('isAutomatedResponse' in msgData) || msgData.isAutomatedResponse is bool) &&
               (!('buttons' in msgData) || (msgData.buttons is list && msgData.buttons.size() <= 10)) &&
               (!('metadata' in msgData) || msgData.metadata is map);
      }

      allow read: if isAuthenticated() &&
        isParticipant(resource.data.conversationId);
      allow create: if isAuthenticated() &&
        isParticipant(request.resource.data.conversationId) &&
        isValidMessage();
      allow update: if isAuthenticated() &&
        isParticipant(resource.data.conversationId) &&
        (request.auth.uid == resource.data.senderId ||
         request.auth.uid == resource.data.receiverId);
      allow delete: if isAuthenticated() &&
        request.auth.uid == resource.data.senderId;
    }

    // Conversations collection rules
    match /conversations/{conversationId} {
      function isConversationParticipant() {
        return isAuthenticated() &&
          (request.auth.uid in resource.data.participants ||
           resource.data.isPublic == true);
      }

      allow read: if isAuthenticated() && isConversationParticipant();
      allow create: if isAuthenticated() &&
        request.auth.uid in request.resource.data.participants;
      allow update: if isAuthenticated() && isConversationParticipant();
      allow delete: if false; // Never allow conversation deletion

      match /{allChildren=**} {
        allow read: if true;
        allow write: if isOwner(userId);
      }
    }

    // Portfolio items collection
    match /portfolio/{itemId} {
      allow read: if true;
      allow create: if isAuthenticated();
      // Allow any authenticated user to update interaction-related fields
      allow update: if isAuthenticated() && (
        resource.data.user_id == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes_count', 'views_count', 'comments_count', 'likes', 'views', 'comments'])
      );
      allow delete: if isAuthenticated() && resource.data.user_id == request.auth.uid;

      match /interactions/{type} {
        allow read: if true;
        allow write: if isAuthenticated();
      }

      match /likes/{likeId} {
        allow read: if true;
        allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
        allow delete: if isAuthenticated();
      }
    }

    // Tools collection
    match /tools/{toolId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && resource.data.user_id == request.auth.uid;
    }

    // AI Tools collection
    match /ai-tools/{toolId} {
      allow read: if true;
      allow write: if isAuthenticated() &&
        (request.auth.token.admin == true ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // For backward compatibility
    match /aiTools/{toolId} {
      allow read: if true;
      allow write: if isAuthenticated() &&
        (request.auth.token.admin == true ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }

    // Likes collection (global)
    match /likes/{likeId} {
      allow read: if true;
      allow create: if isAuthenticated()
        && request.resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated();
    }

    // Video likes collection (if you use a separate collection)
    match /videoLikes/{likeId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated();
    }

    // Likes as a subcollection under videos
    match /videos/{videoId}/likes/{likeId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow delete: if isAuthenticated();
    }

    // Comments collection
    match /comments/{commentId} {
      allow read: if true;
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update: if isAuthenticated() &&
        get(/databases/$(database)/documents/comments/$(commentId)).data.userId == request.auth.uid &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['content', 'updatedAt']);
      allow delete: if isAuthenticated() &&
        get(/databases/$(database)/documents/comments/$(commentId)).data.userId == request.auth.uid;
    }

    // Conversations collection
    match /conversations/{conversationId} {
      allow read: if isAuthenticated() &&
        request.auth.uid in resource.data.participants;
      allow create: if isAuthenticated() &&
        request.auth.uid in request.resource.data.participants;
      allow update: if isAuthenticated() &&
        request.auth.uid in resource.data.participants &&
        request.resource.data.diff(resource.data).affectedKeys()
          .hasAny(['lastMessage', 'updatedAt', 'serverTime']);
    }

    // Messages subcollection
    match /conversations/{conversationId}/messages/{messageId} {
      allow read: if isAuthenticated() &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
      allow create: if isAuthenticated() &&
        request.auth.uid == request.resource.data.senderId &&
        request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
      allow update: if isAuthenticated() &&
        request.auth.uid == resource.data.receiverId &&
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['read']);
    }

    // Analytics views collection
    match /analytics_views/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
    }

    // Analytics interactions collection
    match /analytics_interactions/{interactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
    }

    // Analytics summary for portfolio items
    match /analytics_summary_portfolio/{itemId} {
      allow read: if isAuthenticated() &&
        exists(/databases/$(database)/documents/portfolio/$(itemId)) &&
        get(/databases/$(database)/documents/portfolio/$(itemId)).data.user_id == request.auth.uid;
      allow write: if isAuthenticated();
    }

    // Analytics summary for profiles
    match /analytics_summary_profile/{userId} {
      allow read: if isAuthenticated() && request.auth.uid == userId;
      allow write: if isAuthenticated();
    }    // System collections for testing and notifications
    match /_connection_test/{docId} {
      allow read, write: if true;
    }

    // Toolkits collection
    match /toolkits/{toolkitId} {
      // Helper function to check if user owns the toolkit
      function isToolkitOwner() {
        return isAuthenticated() && resource.data.created_by == request.auth.uid;
      }

      // Allow reading of published toolkits by any authenticated user
      allow read: if isAuthenticated() && (
        resource.data.is_published == true || 
        isToolkitOwner()
      );

      // Allow creation by authenticated users
      allow create: if isAuthenticated() && (
        request.resource.data.created_by == request.auth.uid &&
        request.resource.data.created_at is string &&
        request.resource.data.updated_at is string &&
        request.resource.data.tools is list &&
        (request.resource.data.source == 'user' || 
          (request.resource.data.source == 'library' && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'))
      );

      // Allow updates by toolkit owner or admin for library toolkits
      allow update: if isAuthenticated() && (
        isToolkitOwner() ||
        (resource.data.source == 'library' && 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin')
      );

      // Allow deletion by toolkit owner or admin for library toolkits
      allow delete: if isAuthenticated() && (
        isToolkitOwner() ||
        (resource.data.source == 'library' && 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin')
      );
    }

    // Toolkits collection rules
    match /toolkits/{toolkitId} {
      // Allow read if:
      // 1. The user created the toolkit
      // 2. The toolkit is shared with the user
      // 3. The toolkit is published to portfolio
      allow read: if isAuthenticated() && (
        resource.data.created_by == request.auth.uid ||
        request.auth.uid in (resource.data.shared_with || []) ||
        resource.data.is_published == true
      );
      
      // Allow create if user is authenticated
      allow create: if isAuthenticated() && (
        request.resource.data.created_by == request.auth.uid
      );
      
      // Allow update if:
      // 1. The user created the toolkit
      // 2. The user is adding/removing themselves from shared_with array
      allow update: if isAuthenticated() && (
        resource.data.created_by == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['shared_with'])
      );
      
      // Allow delete only by creator
      allow delete: if isAuthenticated() && (
        resource.data.created_by == request.auth.uid
      );
    }

    // Portfolio collection rules with toolkit support
    match /portfolio/{portfolioId} {
      allow read: if true;  // Public read access
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid
      );
      allow update: if isAuthenticated() && (
        resource.data.userId == request.auth.uid ||
        request.resource.data.diff(resource.data).affectedKeys().hasOnly(['likes', 'views'])
      );
      allow delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid
      );
    }

    // Portfolio drafts collection rules
    match /portfolio_drafts/{draftId} {
      allow read, write, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid
      );
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid
      );
    }

    // Portfolio archives collection rules
    match /portfolio_archives/{archiveId} {
      allow read, write, delete: if isAuthenticated() && (
        resource.data.userId == request.auth.uid
      );
      allow create: if isAuthenticated() && (
        request.resource.data.userId == request.auth.uid
      );
    }

    // Storage cleanup queue rules (admin only)
    match /storage_cleanup_queue/{queueId} {
      allow read, write, delete: if isAuthenticated() && (
        request.auth.token.admin == true
      );
    }

    // Storage usage tracking rules
    match /storage_usage/{userId} {
      allow read: if isAuthenticated() && (
        resource.id == request.auth.uid ||
        request.auth.token.admin == true
      );
      allow write: if isAuthenticated() && (
        resource.id == request.auth.uid ||
        request.auth.token.admin == true
      );
    }
  }
}