# Files to Delete After Portfolio Migration

## ✅ **Migration Complete - Safe to Delete**

After the portfolio system migration is complete and tested, the following files can be safely deleted:

### **Old Portfolio Components (Can Delete)**
```
src/components/portfolio/PortfolioForm.tsx
src/components/dashboard/AddPortfolio.tsx
```
**Reason**: Replaced by `EnhancedPortfolioForm.tsx` and `EnhancedAddPortfolio.tsx`

### **Google Drive Related Files (Can Delete After Testing)**
```
src/components/storage/GoogleDriveStorage.tsx
src/lib/googleDrive.ts
```
**Reason**: Replaced by Firebase Storage and enhanced Livepeer integration
**⚠️ Warning**: Only delete after confirming all users have migrated from Google Drive

### **Redundant Portfolio Components (Can Delete)**
```
src/components/portfolio/AddProject.tsx
src/components/portfolio/AddProjectCard.tsx
src/components/portfolio/AddProjectForm.tsx
```
**Reason**: Functionality merged into the enhanced portfolio system

### **Outdated Service Functions (Can Delete)**
```
src/services/portfolioService.ts (specific functions)
```
**Functions to remove**:
- `migratePortfolioItems`
- `removeDeletedGDriveItems`
- `resetGDriveItems`
- Google Drive specific functions

**Reason**: Replaced by new storage services

## ⚠️ **Keep These Files (DO NOT DELETE)**

### **Core Portfolio Files (Keep)**
```
src/components/dashboard/Portfolio.tsx
src/components/portfolio/PortfolioTabs.tsx
src/components/portfolio/PortfolioItemCard.tsx
src/components/portfolio/PortfolioFilterTools.tsx
src/components/portfolio/PortfolioHeader.tsx
src/components/portfolio/PortfolioEmpty.tsx
src/components/portfolio/PortfolioOnboarding.tsx
src/components/portfolio/PortfolioVideo.tsx
src/components/portfolio/PostTypeSelector.tsx
src/components/portfolio/ToolkitSelector.tsx
```
**Reason**: Still used by the portfolio display and management system

### **Service Files (Keep)**
```
src/services/portfolioService.ts (core functions)
src/services/toolkitService.ts
```
**Reason**: Core portfolio functionality still needed

### **New Enhanced Files (Keep)**
```
src/components/portfolio/EnhancedPortfolioForm.tsx
src/components/portfolio/PortfolioMediaUploader.tsx
src/components/portfolio/DraftManager.tsx
src/components/storage/FirebaseImageUploader.tsx
src/components/dashboard/EnhancedAddPortfolio.tsx
src/services/storageCleanupService.ts
src/services/portfolioStorageService.ts
src/services/scheduledCleanupService.ts
```
**Reason**: New enhanced portfolio system

## 🔄 **Migration Steps**

### **Phase 1: Test New System (Current)**
1. ✅ New components deployed
2. ✅ Routes updated to use enhanced components
3. ✅ Cleanup services initialized
4. 🔄 **Test the new portfolio creation and editing**

### **Phase 2: Verify Migration (Next)**
1. Test image uploads with Firebase Storage
2. Test video uploads with enhanced Livepeer
3. Test draft auto-save functionality
4. Test cleanup services
5. Verify all portfolio features work

### **Phase 3: Clean Up (After Testing)**
1. Delete old portfolio components
2. Remove Google Drive dependencies
3. Clean up unused service functions
4. Update documentation

## 📋 **Deletion Checklist**

Before deleting any files, ensure:

- [ ] New portfolio system is fully tested
- [ ] All users can create/edit portfolio items
- [ ] Image uploads work with Firebase Storage
- [ ] Video uploads work with enhanced Livepeer
- [ ] Draft system is functioning
- [ ] Cleanup services are running
- [ ] No references to old components in codebase
- [ ] Backup of old files is available

## 🚨 **Files with Dependencies (Check Before Deleting)**

### **GoogleDriveStorage.tsx**
**Check these files for imports**:
```bash
grep -r "GoogleDriveStorage" src/
```

### **PortfolioForm.tsx**
**Check these files for imports**:
```bash
grep -r "PortfolioForm" src/
```

### **AddPortfolio.tsx**
**Check these files for imports**:
```bash
grep -r "AddPortfolio" src/
```

## 🔧 **Automated Cleanup Script**

Create this script to safely remove old files:

```bash
#!/bin/bash
# portfolio-cleanup.sh

echo "🧹 Portfolio System Cleanup"
echo "=========================="

# Check for remaining references
echo "Checking for old component references..."

if grep -r "GoogleDriveStorage" src/ --exclude-dir=node_modules; then
    echo "❌ Found GoogleDriveStorage references. Please update before deleting."
    exit 1
fi

if grep -r "PortfolioForm" src/ --exclude-dir=node_modules | grep -v "Enhanced"; then
    echo "❌ Found old PortfolioForm references. Please update before deleting."
    exit 1
fi

if grep -r "AddPortfolio" src/ --exclude-dir=node_modules | grep -v "Enhanced"; then
    echo "❌ Found old AddPortfolio references. Please update before deleting."
    exit 1
fi

echo "✅ No old component references found."

# Create backup
echo "Creating backup..."
mkdir -p backup/old-portfolio-system
cp src/components/portfolio/PortfolioForm.tsx backup/old-portfolio-system/
cp src/components/dashboard/AddPortfolio.tsx backup/old-portfolio-system/
cp src/components/storage/GoogleDriveStorage.tsx backup/old-portfolio-system/
cp src/lib/googleDrive.ts backup/old-portfolio-system/

echo "✅ Backup created in backup/old-portfolio-system/"

# Remove old files
echo "Removing old files..."
rm src/components/portfolio/PortfolioForm.tsx
rm src/components/dashboard/AddPortfolio.tsx
rm src/components/portfolio/AddProject.tsx
rm src/components/portfolio/AddProjectCard.tsx
rm src/components/portfolio/AddProjectForm.tsx

echo "✅ Old portfolio components removed."

# Optional: Remove Google Drive files (uncomment when ready)
# rm src/components/storage/GoogleDriveStorage.tsx
# rm src/lib/googleDrive.ts
# echo "✅ Google Drive files removed."

echo "🎉 Cleanup complete!"
echo "📝 Don't forget to:"
echo "   - Test the new portfolio system"
echo "   - Update any remaining references"
echo "   - Deploy Firebase rules if not done already"
```

## 📊 **File Size Savings**

Estimated file size reduction after cleanup:
- **Old Components**: ~15KB
- **Google Drive Files**: ~25KB
- **Redundant Files**: ~10KB
- **Total Savings**: ~50KB

## 🔄 **Rollback Plan**

If issues arise, restore from backup:
```bash
cp backup/old-portfolio-system/* src/components/portfolio/
cp backup/old-portfolio-system/GoogleDriveStorage.tsx src/components/storage/
cp backup/old-portfolio-system/googleDrive.ts src/lib/
```

Then revert the routing changes in `App.tsx` and `EditPortfolio.tsx`.
