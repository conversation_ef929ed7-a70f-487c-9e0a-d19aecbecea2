import { useRef, useState } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { LivepeerPlayer } from '@/components/video/LivepeerPlayer';
import { isLivepeerUrl, extractPlaybackId } from '@/lib/livepeer';

interface PortfolioVideoProps {
    mediaUrl: string;
    thumbnailUrl?: string;
    title: string;
    isReel?: boolean;
    onPlayChange?: (isPlaying: boolean) => void;
}

const DEFAULT_THUMBNAIL = '/images/video-placeholder.jpg'; // You should add a default thumbnail image

export function PortfolioVideo({
    mediaUrl,
    thumbnailUrl,
    title,
    isReel = false,
    onPlayChange
}: PortfolioVideoProps) {
    const [isPlaying, setIsPlaying] = useState(false);
    const [isMuted, setIsMuted] = useState(true);
    const [thumbnailError, setThumbnailError] = useState(false);
    const videoRef = useRef<HTMLVideoElement>(null);

    const handlePlayChange = (playing: boolean) => {
        setIsPlaying(playing);
        onPlayChange?.(playing);
    };

    const togglePlay = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (videoRef.current) {
            if (isPlaying) {
                videoRef.current.pause();
            } else {
                videoRef.current.play().catch(err => {
                    console.error('Error playing video:', err);
                    handlePlayChange(false);
                });
            }
            handlePlayChange(!isPlaying);
        }
    };

    const toggleMute = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (videoRef.current) {
            videoRef.current.muted = !isMuted;
            setIsMuted(!isMuted);
        }
    };



    // Handle thumbnail errors and filter out .m3u8 URLs
    const getValidThumbnailUrl = (url?: string): string => {
        if (!url || url.includes('.m3u8') || thumbnailError) {
            return DEFAULT_THUMBNAIL;
        }
        return url;
    };

    if (isLivepeerUrl(mediaUrl)) {
        // For HLS URLs, pass the URL directly; for playback IDs, extract them
        const playbackId = mediaUrl.includes('.m3u8') ? undefined : extractPlaybackId(mediaUrl);

        return (
            <LivepeerPlayer
                src={mediaUrl.includes('.m3u8') ? mediaUrl : undefined}
                playbackId={playbackId}
                poster={getValidThumbnailUrl(thumbnailUrl)}
                title={title}
                muted={isMuted}
                autoPlay={isReel}
                loop={true}
                onPlay={() => handlePlayChange(true)}
                onPause={() => handlePlayChange(false)}
                className={isReel ? 'aspect-[9/16]' : ''}
            />
        );
    }

    // Get Google Drive file ID if present
    const getGoogleDriveFileId = (url: string): string | null => {
        if (!url) return null;
        const fileIdMatch = url.match(/\/d\/([^\/]+)/);
        return fileIdMatch ? fileIdMatch[1] : null;
    };

    if (mediaUrl.includes('drive.google.com')) {
        const fileId = getGoogleDriveFileId(mediaUrl);
        if (!fileId) return null;

        // Use direct video player for Google Drive videos
        return (
            <div className="relative w-full h-full">
                <video
                    ref={videoRef}
                    src={`https://drive.google.com/uc?export=download&id=${fileId}`}
                    className="w-full h-full object-cover"
                    muted={isMuted}
                    loop
                    playsInline
                    controls
                    controlsList="nodownload nofullscreen"
                    onClick={(e) => e.stopPropagation()}
                    poster={thumbnailUrl}
                    onPlay={() => handlePlayChange(true)}
                    onPause={() => handlePlayChange(false)}
                />

                {/* Custom controls overlay */}
                <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between bg-black/50 rounded-md px-2 py-1 z-10">
                    <button
                        onClick={togglePlay}
                        className="p-1 hover:bg-white/10 rounded-full transition-colors"
                    >
                        {isPlaying ? (
                            <Pause className="w-4 h-4 text-white" />
                        ) : (
                            <Play className="w-4 h-4 text-white" />
                        )}
                    </button>
                    <button
                        onClick={toggleMute}
                        className="p-1 hover:bg-white/10 rounded-full transition-colors"
                    >
                        {isMuted ? (
                            <VolumeX className="w-4 h-4 text-white" />
                        ) : (
                            <Volume2 className="w-4 h-4 text-white" />
                        )}
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="relative w-full h-full">
            <video
                ref={videoRef}
                src={mediaUrl}
                className="w-full h-full object-cover"
                muted={isMuted}
                loop
                playsInline
                controls
                controlsList="nodownload nofullscreen"
                onClick={(e) => e.stopPropagation()}
                poster={thumbnailUrl}
                onPlay={() => handlePlayChange(true)}
                onPause={() => handlePlayChange(false)}
            />

            {/* Custom controls overlay */}
            <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between bg-black/50 rounded-md px-2 py-1 z-10">
                <button
                    onClick={togglePlay}
                    className="p-1 hover:bg-white/10 rounded-full transition-colors"
                >
                    {isPlaying ? (
                        <Pause className="w-4 h-4 text-white" />
                    ) : (
                        <Play className="w-4 h-4 text-white" />
                    )}
                </button>
                <button
                    onClick={toggleMute}
                    className="p-1 hover:bg-white/10 rounded-full transition-colors"
                >
                    {isMuted ? (
                        <VolumeX className="w-4 h-4 text-white" />
                    ) : (
                        <Volume2 className="w-4 h-4 text-white" />
                    )}
                </button>
            </div>
        </div>
    );
}
