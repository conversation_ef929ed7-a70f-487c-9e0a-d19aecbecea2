rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /avatars/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    match /tool_logos/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    match /portfolio/{userId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    match /portfolio-drafts/{userId}/{allPaths=**} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    match /portfolio-archives/{userId}/{allPaths=**} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.uid == userId;
      allow delete: if request.auth != null && request.auth.uid == userId;
    }

    match /tool-logos/{toolId}/{fileName} {
      allow read;
      allow write: if request.auth != null &&
        (request.auth.token.admin == true ||
         request.resource.size < 5 * 1024 * 1024 &&
         request.resource.contentType.matches('image/.*'));
    }

    // Cleanup service rules - only admins can delete orphaned files
    match /{allPaths=**} {
      allow delete: if request.auth != null && request.auth.token.admin == true;
    }
  }
}