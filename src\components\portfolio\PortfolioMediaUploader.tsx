import { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { FirebaseImageUploader } from '@/components/storage/FirebaseImageUploader';
import { LivepeerUploader } from '@/components/video/LivepeerUploader';
import { Image as ImageIcon, Video, Upload } from 'lucide-react';

interface MediaUploadResult {
    url: string;
    type: 'image' | 'video';
    metadata?: {
        fileName?: string;
        size?: number;
        playbackId?: string;
        playbackUrl?: string;
        title?: string;
        description?: string;
        tags?: string[];
    };
}

interface PortfolioMediaUploaderProps {
    userId: string;
    onMediaUpload: (result: MediaUploadResult) => void;
    onError: (error: string) => void;
    defaultTab?: 'image' | 'video';
    className?: string;
    maxImageSizeMB?: number;
    allowedImageTypes?: string[];
}

export function PortfolioMediaUploader({
    userId,
    onMediaUpload,
    onError,
    defaultTab = 'image',
    className = '',
    maxImageSizeMB = 10,
    allowedImageTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
}: PortfolioMediaUploaderProps) {
    const [activeTab, setActiveTab] = useState<'image' | 'video'>(defaultTab);
    const [isUploading, setIsUploading] = useState(false);
    const { toast } = useToast();

    const handleImageUpload = useCallback((url: string, metadata?: { fileName: string; size: number }) => {
        const result: MediaUploadResult = {
            url,
            type: 'image',
            metadata
        };
        onMediaUpload(result);
        setIsUploading(false);
    }, [onMediaUpload]);

    const handleVideoUpload = useCallback((url: string, metadata?: {
        title: string;
        description: string;
        tags: string[];
        playbackId: string;
        playbackUrl: string;
    }) => {
        const result: MediaUploadResult = {
            url,
            type: 'video',
            metadata
        };
        onMediaUpload(result);
        setIsUploading(false);
    }, [onMediaUpload]);

    const handleUploadStart = useCallback(() => {
        setIsUploading(true);
    }, []);

    const handleUploadError = useCallback((error: string) => {
        setIsUploading(false);
        onError(error);
    }, [onError]);

    return (
        <Card className={`p-6 bg-gray-800 border-gray-600 ${className}`}>
            <div className="space-y-4">
                <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2 text-white">Upload Media</h3>
                    <p className="text-sm text-gray-300">
                        Choose images or videos for your portfolio item
                    </p>
                </div>

                <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'image' | 'video')}>
                    <TabsList className="grid w-full grid-cols-2 bg-gray-700 border-gray-600">
                        <TabsTrigger value="image" className="flex items-center gap-2 data-[state=active]:bg-gray-600 data-[state=active]:text-white text-gray-300">
                            <ImageIcon className="w-4 h-4" />
                            Images
                        </TabsTrigger>
                        <TabsTrigger value="video" className="flex items-center gap-2 data-[state=active]:bg-gray-600 data-[state=active]:text-white text-gray-300">
                            <Video className="w-4 h-4" />
                            Videos
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="image" className="mt-4">
                        <div className="space-y-4">
                            <div className="text-sm text-gray-300">
                                <p>Upload high-quality images to showcase your work.</p>
                                <ul className="list-disc list-inside mt-2 space-y-1">
                                    <li>Supported formats: JPEG, PNG, WebP, GIF</li>
                                    <li>Maximum size: {maxImageSizeMB}MB</li>
                                    <li>Images will be automatically optimized</li>
                                    <li>Recommended resolution: 1920x1080 or higher</li>
                                </ul>
                            </div>

                            <FirebaseImageUploader
                                userId={userId}
                                onUploadComplete={handleImageUpload}
                                onError={handleUploadError}
                                maxSizeInMB={maxImageSizeMB}
                                allowedTypes={allowedImageTypes}
                                storagePath="portfolio"
                                className="w-full"
                            />
                        </div>
                    </TabsContent>

                    <TabsContent value="video" className="mt-4">
                        <div className="space-y-4">
                            <div className="text-sm text-gray-300">
                                <p>Upload videos to create engaging portfolio content.</p>
                                <ul className="list-disc list-inside mt-2 space-y-1">
                                    <li>Supported formats: MP4, WebM, MOV</li>
                                    <li>Maximum size: 500MB</li>
                                    <li>Videos are processed for optimal streaming</li>
                                    <li>Automatic thumbnail generation</li>
                                    <li>Global CDN delivery via Livepeer</li>
                                </ul>
                            </div>

                            <LivepeerUploader
                                onUploadComplete={handleVideoUpload}
                                onError={handleUploadError}
                                userId={userId}
                            />
                        </div>
                    </TabsContent>
                </Tabs>

                {isUploading && (
                    <div className="text-center py-4">
                        <div className="flex items-center justify-center gap-2 text-blue-400">
                            <Upload className="w-4 h-4 animate-pulse" />
                            <span>Uploading media...</span>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    );
}

// Hook for managing multiple media uploads
export function usePortfolioMediaUploader(userId: string) {
    const [uploadedMedia, setUploadedMedia] = useState<MediaUploadResult[]>([]);
    const [isUploading, setIsUploading] = useState(false);
    const { toast } = useToast();

    const addMedia = useCallback((result: MediaUploadResult) => {
        setUploadedMedia(prev => [...prev, result]);
        toast({
            title: 'Media uploaded successfully',
            description: `${result.type === 'image' ? 'Image' : 'Video'} has been added to your portfolio item.`
        });
    }, [toast]);

    const removeMedia = useCallback((index: number) => {
        setUploadedMedia(prev => prev.filter((_, i) => i !== index));
    }, []);

    const clearMedia = useCallback(() => {
        setUploadedMedia([]);
    }, []);

    const handleError = useCallback((error: string) => {
        toast({
            title: 'Upload failed',
            description: error,
            variant: 'destructive'
        });
    }, [toast]);

    return {
        uploadedMedia,
        isUploading,
        addMedia,
        removeMedia,
        clearMedia,
        handleError,
        setIsUploading
    };
}

// Component for displaying uploaded media preview
interface MediaPreviewProps {
    media: MediaUploadResult[];
    onRemove: (index: number) => void;
    className?: string;
}

export function MediaPreview({ media, onRemove, className = '' }: MediaPreviewProps) {
    if (media.length === 0) return null;

    return (
        <div className={`space-y-4 ${className}`}>
            <h4 className="font-medium text-white">Uploaded Media</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {media.map((item, index) => (
                    <div key={index} className="relative group">
                        {item.type === 'image' ? (
                            <img
                                src={item.url}
                                alt={`Upload ${index + 1}`}
                                className="w-full h-32 object-cover rounded-lg"
                            />
                        ) : (
                            <div className="w-full h-32 bg-gray-700 rounded-lg flex items-center justify-center">
                                <Video className="w-8 h-8 text-gray-300" />
                                <span className="ml-2 text-sm text-gray-300">Video</span>
                            </div>
                        )}
                        <Button
                            onClick={() => onRemove(index)}
                            variant="destructive"
                            size="sm"
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                            ×
                        </Button>
                        <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
                            {item.type}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
