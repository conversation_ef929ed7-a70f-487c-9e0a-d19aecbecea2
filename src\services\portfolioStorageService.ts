import {
    collection,
    doc,
    addDoc,
    updateDoc,
    deleteDoc,
    getDoc,
    getDocs,
    query,
    where,
    orderBy,
    writeBatch,
    Timestamp
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';
import { PortfolioItem } from '@/types';
import { storageCleanupService } from './storageCleanupService';

// Enhanced portfolio item interface for storage operations
interface PortfolioStorageItem extends Omit<PortfolioItem, 'id'> {
    id?: string;
    storage_metadata?: {
        file_paths: string[];
        file_sizes: number[];
        upload_timestamps: string[];
        livepeer_asset_id?: string;
    };
}

// Draft interface
interface PortfolioDraft {
    id?: string;
    userId: string;
    title: string;
    description: string;
    media_url?: string;
    media_urls?: string[];
    media_type?: 'image' | 'video' | 'audio';
    content_type: 'post' | 'reel' | 'both';
    tools_used: string[];
    tagged_toolkits?: string[];
    project_url?: string;
    is_public: boolean;
    created_at: string;
    updated_at: string;
    auto_save_count: number;
    storage_metadata?: {
        file_paths: string[];
        file_sizes: number[];
        upload_timestamps: string[];
        livepeer_asset_id?: string;
    };
}

export class PortfolioStorageService {
    private static instance: PortfolioStorageService;
    private portfolioCollection = collection(db, 'portfolio');
    private draftsCollection = collection(db, 'portfolio_drafts');
    private archivesCollection = collection(db, 'portfolio_archives');

    static getInstance(): PortfolioStorageService {
        if (!PortfolioStorageService.instance) {
            PortfolioStorageService.instance = new PortfolioStorageService();
        }
        return PortfolioStorageService.instance;
    }

    /**
     * Save a draft portfolio item
     */
    async saveDraft(userId: string, draftData: Partial<PortfolioDraft>): Promise<string> {
        try {
            const now = new Date().toISOString();

            // Filter out undefined values to prevent Firestore errors
            const cleanDraftData: Record<string, any> = {
                userId,
                title: draftData.title || '',
                description: draftData.description || '',
                content_type: draftData.content_type || 'post',
                tools_used: draftData.tools_used || [],
                tagged_toolkits: draftData.tagged_toolkits || [],
                project_url: draftData.project_url || '',
                is_public: draftData.is_public ?? true,
                created_at: draftData.created_at || now,
                updated_at: now,
                auto_save_count: (draftData.auto_save_count || 0) + 1
            };

            // Only add optional fields if they have values
            if (draftData.media_url) {
                cleanDraftData.media_url = draftData.media_url;
            }

            if (draftData.media_urls && draftData.media_urls.length > 0) {
                cleanDraftData.media_urls = draftData.media_urls;
            }

            if (draftData.media_type) {
                cleanDraftData.media_type = draftData.media_type;
            }

            if (draftData.storage_metadata) {
                cleanDraftData.storage_metadata = draftData.storage_metadata;
            }

            if (draftData.id) {
                // Update existing draft
                const draftRef = doc(this.draftsCollection, draftData.id);
                await updateDoc(draftRef, cleanDraftData);
                return draftData.id;
            } else {
                // Create new draft
                const draftRef = await addDoc(this.draftsCollection, cleanDraftData);
                return draftRef.id;
            }
        } catch (error) {
            console.error('Error saving draft:', error);
            throw error;
        }
    }

    /**
     * Get user's drafts
     */
    async getUserDrafts(userId: string): Promise<PortfolioDraft[]> {
        try {
            const q = query(
                this.draftsCollection,
                where('userId', '==', userId),
                orderBy('updated_at', 'desc')
            );
            const querySnapshot = await getDocs(q);

            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            })) as PortfolioDraft[];
        } catch (error) {
            console.error('Error fetching drafts:', error);
            return [];
        }
    }

    /**
     * Delete a draft and schedule media cleanup
     */
    async deleteDraft(draftId: string): Promise<void> {
        try {
            const draftRef = doc(this.draftsCollection, draftId);
            const draftDoc = await getDoc(draftRef);

            if (!draftDoc.exists()) {
                throw new Error('Draft not found');
            }

            const draft = draftDoc.data() as PortfolioDraft;

            // Schedule media files for cleanup
            if (draft.media_url) {
                await storageCleanupService.scheduleCleanup(
                    this.extractPathFromUrl(draft.media_url),
                    draft.media_url,
                    draft.userId,
                    'draft_abandoned',
                    1 // 1 hour delay
                );
            }

            if (draft.media_urls && draft.media_urls.length > 0) {
                for (const url of draft.media_urls) {
                    await storageCleanupService.scheduleCleanup(
                        this.extractPathFromUrl(url),
                        url,
                        draft.userId,
                        'draft_abandoned',
                        1
                    );
                }
            }

            // Delete the draft document
            await deleteDoc(draftRef);
        } catch (error) {
            console.error('Error deleting draft:', error);
            throw error;
        }
    }

    /**
     * Publish a draft as a portfolio item
     */
    async publishDraft(draftId: string): Promise<string> {
        try {
            const draftRef = doc(this.draftsCollection, draftId);
            const draftDoc = await getDoc(draftRef);

            if (!draftDoc.exists()) {
                throw new Error('Draft not found');
            }

            const draft = draftDoc.data() as PortfolioDraft;
            const now = new Date().toISOString();

            // Create portfolio item from draft - filter out undefined values
            const portfolioItemData: any = {
                userId: draft.userId,
                title: draft.title,
                description: draft.description,
                media_url: draft.media_url || '',
                media_urls: draft.media_urls || [],
                media_type: draft.media_type || 'image',
                content_type: draft.content_type,
                tools_used: draft.tools_used,
                tagged_toolkits: draft.tagged_toolkits,
                categories: [],
                likes: 0,
                comments: 0,
                views: 0,
                project_url: draft.project_url || '',
                is_public: draft.is_public,
                status: 'published',
                created_at: now,
                updated_at: now
            };

            // Only add thumbnail_url if it has a value (for images, use the first media URL)
            if (draft.media_type === 'image' && (draft.media_url || (draft.media_urls && draft.media_urls.length > 0))) {
                portfolioItemData.thumbnail_url = draft.media_url || draft.media_urls[0];
            } else if (draft.media_type === 'video' && draft.storage_metadata?.thumbnail_url) {
                portfolioItemData.thumbnail_url = draft.storage_metadata.thumbnail_url;
            }

            // Only add storage_metadata if it exists
            if (draft.storage_metadata) {
                portfolioItemData.storage_metadata = draft.storage_metadata;
            }

            // Add to portfolio collection
            const portfolioRef = await addDoc(this.portfolioCollection, portfolioItemData);

            // Delete the draft
            await deleteDoc(draftRef);

            return portfolioRef.id;
        } catch (error) {
            console.error('Error publishing draft:', error);
            throw error;
        }
    }

    /**
     * Archive a portfolio item
     */
    async archivePortfolioItem(portfolioId: string): Promise<void> {
        try {
            const portfolioRef = doc(this.portfolioCollection, portfolioId);
            const portfolioDoc = await getDoc(portfolioRef);

            if (!portfolioDoc.exists()) {
                throw new Error('Portfolio item not found');
            }

            const portfolioItem = portfolioDoc.data() as PortfolioStorageItem;
            const now = new Date().toISOString();

            // Add to archives collection
            const archivedItem = {
                ...portfolioItem,
                archived_at: now,
                updated_at: now
            };

            await addDoc(this.archivesCollection, archivedItem);

            // Remove from portfolio collection
            await deleteDoc(portfolioRef);
        } catch (error) {
            console.error('Error archiving portfolio item:', error);
            throw error;
        }
    }

    /**
     * Restore archived item
     */
    async restoreArchivedItem(archiveId: string): Promise<string> {
        try {
            const archiveRef = doc(this.archivesCollection, archiveId);
            const archiveDoc = await getDoc(archiveRef);

            if (!archiveDoc.exists()) {
                throw new Error('Archived item not found');
            }

            const archivedItem = archiveDoc.data() as PortfolioStorageItem;
            const now = new Date().toISOString();

            // Remove archive-specific fields and update timestamps
            const { archived_at, ...portfolioItem } = archivedItem;
            portfolioItem.updated_at = now;
            portfolioItem.status = 'published';

            // Add back to portfolio collection
            const portfolioRef = await addDoc(this.portfolioCollection, portfolioItem);

            // Remove from archives collection
            await deleteDoc(archiveRef);

            return portfolioRef.id;
        } catch (error) {
            console.error('Error restoring archived item:', error);
            throw error;
        }
    }

    /**
     * Permanently delete a portfolio item and its media
     */
    async permanentlyDeleteItem(itemId: string, collection: 'portfolio' | 'archives'): Promise<void> {
        try {
            const collectionRef = collection === 'portfolio' ? this.portfolioCollection : this.archivesCollection;
            const itemRef = doc(collectionRef, itemId);
            const itemDoc = await getDoc(itemRef);

            if (!itemDoc.exists()) {
                throw new Error('Item not found');
            }

            const item = itemDoc.data() as PortfolioStorageItem;

            // Schedule media files for immediate cleanup
            if (item.media_url) {
                await storageCleanupService.scheduleCleanup(
                    this.extractPathFromUrl(item.media_url),
                    item.media_url,
                    item.userId,
                    'item_deleted',
                    0 // Immediate cleanup
                );
            }

            if (item.media_urls && item.media_urls.length > 0) {
                for (const url of item.media_urls) {
                    await storageCleanupService.scheduleCleanup(
                        this.extractPathFromUrl(url),
                        url,
                        item.userId,
                        'item_deleted',
                        0
                    );
                }
            }

            // If it's a video with Livepeer asset, schedule that for cleanup too
            if (item.storage_metadata?.livepeer_asset_id) {
                // Note: Livepeer cleanup would need to be implemented separately
                console.log(`TODO: Cleanup Livepeer asset ${item.storage_metadata.livepeer_asset_id}`);
            }

            // Delete the document
            await deleteDoc(itemRef);
        } catch (error) {
            console.error('Error permanently deleting item:', error);
            throw error;
        }
    }

    /**
     * Get user's archived items
     */
    async getUserArchives(userId: string): Promise<PortfolioStorageItem[]> {
        try {
            const q = query(
                this.archivesCollection,
                where('userId', '==', userId),
                orderBy('archived_at', 'desc')
            );
            const querySnapshot = await getDocs(q);

            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            })) as PortfolioStorageItem[];
        } catch (error) {
            console.error('Error fetching archives:', error);
            return [];
        }
    }

    /**
     * Auto-save draft (called periodically)
     */
    async autoSaveDraft(userId: string, draftData: Partial<PortfolioDraft>): Promise<string | null> {
        try {
            // Only auto-save if there's meaningful content
            const hasContent =
                (draftData.title && draftData.title.trim()) ||
                (draftData.description && draftData.description.trim()) ||
                (draftData.media_url) ||
                (draftData.media_urls && draftData.media_urls.length > 0) ||
                (draftData.tools_used && draftData.tools_used.length > 0);

            if (!hasContent) {
                return null;
            }

            return await this.saveDraft(userId, draftData);
        } catch (error) {
            console.error('Error auto-saving draft:', error);
            return null;
        }
    }

    /**
     * Extract storage path from download URL
     */
    private extractPathFromUrl(url: string): string {
        try {
            const urlObj = new URL(url);
            const pathMatch = urlObj.pathname.match(/\/o\/(.+?)\?/);
            return pathMatch ? decodeURIComponent(pathMatch[1]) : '';
        } catch {
            return '';
        }
    }

    /**
     * Clean up user's storage
     */
    async cleanupUserStorage(userId: string): Promise<void> {
        await storageCleanupService.cleanupOrphanedFiles(userId);
        await storageCleanupService.updateStorageUsage(userId);
    }
}

// Export singleton instance
export const portfolioStorageService = PortfolioStorageService.getInstance();
