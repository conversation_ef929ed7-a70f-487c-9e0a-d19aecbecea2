import { storageCleanupService } from './storageCleanupService';
import { portfolioStorageService } from './portfolioStorageService';

/**
 * Scheduled cleanup service that runs maintenance tasks
 * This should be called periodically (e.g., via a cron job or scheduled function)
 */
export class ScheduledCleanupService {
    private static instance: ScheduledCleanupService;
    private isRunning = false;
    private lastRun: Date | null = null;

    static getInstance(): ScheduledCleanupService {
        if (!ScheduledCleanupService.instance) {
            ScheduledCleanupService.instance = new ScheduledCleanupService();
        }
        return ScheduledCleanupService.instance;
    }

    /**
     * Run all cleanup tasks
     */
    async runAllCleanupTasks(): Promise<void> {
        if (this.isRunning) {
            console.log('Cleanup already running, skipping...');
            return;
        }

        try {
            this.isRunning = true;
            console.log('Starting scheduled cleanup tasks...');

            // Run all cleanup tasks in parallel
            await Promise.all([
                this.runStorageCleanup(),
                this.runDraftCleanup(),
                this.runArchiveCleanup()
            ]);

            this.lastRun = new Date();
            console.log('All cleanup tasks completed successfully');

        } catch (error) {
            console.error('Error during scheduled cleanup:', error);
            throw error;
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * Run storage-specific cleanup tasks
     */
    private async runStorageCleanup(): Promise<void> {
        console.log('Running storage cleanup...');
        await storageCleanupService.runCleanupTasks();
    }

    /**
     * Run draft cleanup tasks
     */
    private async runDraftCleanup(): Promise<void> {
        console.log('Running draft cleanup...');
        await storageCleanupService.cleanupAbandonedDrafts();
    }

    /**
     * Run archive cleanup tasks
     */
    private async runArchiveCleanup(): Promise<void> {
        console.log('Running archive cleanup...');
        await storageCleanupService.cleanupExpiredArchives();
    }

    /**
     * Run cleanup for a specific user
     */
    async runUserCleanup(userId: string): Promise<void> {
        console.log(`Running cleanup for user: ${userId}`);
        
        try {
            await Promise.all([
                storageCleanupService.cleanupOrphanedFiles(userId),
                storageCleanupService.updateStorageUsage(userId),
                portfolioStorageService.cleanupUserStorage(userId)
            ]);
            
            console.log(`User cleanup completed for: ${userId}`);
        } catch (error) {
            console.error(`Error during user cleanup for ${userId}:`, error);
            throw error;
        }
    }

    /**
     * Get cleanup status
     */
    getStatus(): {
        isRunning: boolean;
        lastRun: Date | null;
        nextRecommendedRun: Date | null;
    } {
        const nextRun = this.lastRun 
            ? new Date(this.lastRun.getTime() + 24 * 60 * 60 * 1000) // 24 hours later
            : new Date(); // Run immediately if never run

        return {
            isRunning: this.isRunning,
            lastRun: this.lastRun,
            nextRecommendedRun: nextRun
        };
    }

    /**
     * Check if cleanup should run based on last run time
     */
    shouldRun(): boolean {
        if (!this.lastRun) return true; // Never run before
        
        const hoursSinceLastRun = (Date.now() - this.lastRun.getTime()) / (1000 * 60 * 60);
        return hoursSinceLastRun >= 24; // Run every 24 hours
    }
}

// Export singleton instance
export const scheduledCleanupService = ScheduledCleanupService.getInstance();

// Utility function to set up automatic cleanup (call this in your app initialization)
export function setupAutomaticCleanup(): void {
    // Run cleanup every hour, but the service will only actually run if 24 hours have passed
    setInterval(async () => {
        if (scheduledCleanupService.shouldRun()) {
            try {
                await scheduledCleanupService.runAllCleanupTasks();
            } catch (error) {
                console.error('Automatic cleanup failed:', error);
            }
        }
    }, 60 * 60 * 1000); // Check every hour

    console.log('Automatic cleanup scheduled');
}

// Browser-specific cleanup (for localStorage drafts)
export function setupBrowserCleanup(): void {
    // Clean up old localStorage drafts on app start
    const cleanupLocalStorageDrafts = () => {
        const keys = Object.keys(localStorage);
        const draftKeys = keys.filter(key => 
            key.startsWith('videoUploadDraft') || 
            key.startsWith('imageUploadDraft')
        );

        draftKeys.forEach(key => {
            try {
                const draft = JSON.parse(localStorage.getItem(key) || '{}');
                const age = Date.now() - (draft.timestamp || 0);
                
                // Remove drafts older than 7 days
                if (age > 7 * 24 * 60 * 60 * 1000) {
                    localStorage.removeItem(key);
                    console.log(`Removed old localStorage draft: ${key}`);
                }
            } catch (error) {
                // Invalid JSON, remove it
                localStorage.removeItem(key);
                console.log(`Removed invalid localStorage draft: ${key}`);
            }
        });
    };

    // Run on app start
    cleanupLocalStorageDrafts();

    // Run daily
    setInterval(cleanupLocalStorageDrafts, 24 * 60 * 60 * 1000);
}

// Example usage in a React component or app initialization:
/*
import { setupAutomaticCleanup, setupBrowserCleanup } from '@/services/scheduledCleanupService';

// In your app initialization (e.g., App.tsx or main.tsx)
useEffect(() => {
    setupAutomaticCleanup();
    setupBrowserCleanup();
}, []);
*/

// Example manual cleanup trigger (for admin interface):
/*
import { scheduledCleanupService } from '@/services/scheduledCleanupService';

const AdminCleanupButton = () => {
    const [isRunning, setIsRunning] = useState(false);
    
    const handleCleanup = async () => {
        setIsRunning(true);
        try {
            await scheduledCleanupService.runAllCleanupTasks();
            toast.success('Cleanup completed successfully');
        } catch (error) {
            toast.error('Cleanup failed');
        } finally {
            setIsRunning(false);
        }
    };
    
    return (
        <Button onClick={handleCleanup} disabled={isRunning}>
            {isRunning ? 'Running Cleanup...' : 'Run Cleanup'}
        </Button>
    );
};
*/
