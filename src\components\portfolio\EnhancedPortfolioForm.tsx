import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { User } from '@/types';
import { PortfolioMediaUploader, MediaPreview, usePortfolioMediaUploader } from './PortfolioMediaUploader';
import { DraftManager, useDraftManager } from './DraftManager';
import { ToolkitSelector } from './ToolkitSelector';
import { portfolioStorageService } from '@/services/portfolioStorageService';
import { Loader2, Save, Eye, FileText, Archive, AlertTriangle, X } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { collection, getDocs, query, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Enhanced schema for the new portfolio form
const enhancedPortfolioSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  tools_used: z.array(z.string()).min(1, 'Select at least one tool'),
  tagged_toolkits: z.array(z.string()).optional().default([]),
  is_public: z.boolean().default(true),
  status: z.enum(['published', 'draft', 'archived']).default('published'),
  content_type: z.enum(['post', 'reel', 'both']).default('post'),
  project_url: z.string().optional().or(z.literal('')),
});

type EnhancedPortfolioFormData = z.infer<typeof enhancedPortfolioSchema>;

interface EnhancedPortfolioFormProps {
  user: User;
  initialData?: Partial<EnhancedPortfolioFormData>;
  onSubmit: (data: EnhancedPortfolioFormData & {
    media_urls: string[];
    media_metadata: any[];
  }) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
}

export function EnhancedPortfolioForm({
  user,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false
}: EnhancedPortfolioFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedToolkits, setSelectedToolkits] = useState<string[]>(initialData?.tagged_toolkits || []);
  const [activeTab, setActiveTab] = useState<'form' | 'drafts'>('form');
  const [userTools, setUserTools] = useState<any[]>([]);
  const [selectedTools, setSelectedTools] = useState<string[]>(initialData?.tools_used || []);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);

  // Use the new media uploader hook
  const {
    uploadedMedia,
    addMedia,
    removeMedia,
    clearMedia,
    handleError: handleMediaError
  } = usePortfolioMediaUploader(user.id);

  // Use the draft manager hook
  const {
    currentDraftId,
    autoSaveStatus,
    saveDraft,
    clearCurrentDraft
  } = useDraftManager(user.id);

  // Form setup
  const { register, handleSubmit, setValue, watch, formState: { errors } } = useForm<EnhancedPortfolioFormData>({
    resolver: zodResolver(enhancedPortfolioSchema),
    defaultValues: {
      ...initialData,
      is_public: initialData?.is_public ?? true,
      status: initialData?.status ?? 'published',
      content_type: initialData?.content_type ?? 'post'
    },
    mode: 'onChange'
  });

  // Watch form values for auto-save and unsaved changes detection
  const watchedValues = watch();

  // Fetch user's tools
  useEffect(() => {
    const fetchUserTools = async () => {
      try {
        const toolsQuery = query(
          collection(db, 'tools'),
          where('userId', '==', user.id)
        );
        const toolsSnapshot = await getDocs(toolsQuery);
        const tools = toolsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        setUserTools(tools);
      } catch (error) {
        console.error('Error fetching user tools:', error);
      }
    };

    fetchUserTools();
  }, [user.id]);

  // Track unsaved changes
  useEffect(() => {
    const hasChanges =
      watchedValues.title ||
      watchedValues.description ||
      uploadedMedia.length > 0 ||
      selectedTools.length > 0 ||
      selectedToolkits.length > 0;

    setHasUnsavedChanges(hasChanges);
  }, [watchedValues, uploadedMedia, selectedTools, selectedToolkits]);

  // Auto-save draft every 30 seconds
  useEffect(() => {
    const interval = setInterval(async () => {
      if (hasUnsavedChanges) {
        try {
          await saveDraft({
            ...watchedValues,
            tools_used: selectedTools,
            tagged_toolkits: selectedToolkits,
            media_urls: uploadedMedia.map(m => m.url),
            storage_metadata: {
              file_paths: uploadedMedia.map(m => m.metadata?.fileName || ''),
              file_sizes: uploadedMedia.map(m => m.metadata?.size || 0),
              upload_timestamps: uploadedMedia.map(() => new Date().toISOString()),
              livepeer_asset_id: uploadedMedia.find(m => m.type === 'video')?.metadata?.playbackId
            }
          });
        } catch (error) {
          console.error('Auto-save failed:', error);
        }
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [watchedValues, selectedToolkits, selectedTools, uploadedMedia, saveDraft, hasUnsavedChanges]);

  const handleFormSubmit = async (formData: EnhancedPortfolioFormData) => {
    if (uploadedMedia.length === 0) {
      toast({
        title: 'Error',
        description: 'Please upload at least one media file',
        variant: 'destructive'
      });
      return;
    }

    if (selectedTools.length === 0) {
      toast({
        title: 'Error',
        description: 'Please select at least one tool you used',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Prepare submission data
      const submissionData = {
        ...formData,
        tools_used: selectedTools,
        tagged_toolkits: selectedToolkits,
        media_urls: uploadedMedia.map(m => m.url),
        media_metadata: uploadedMedia.map(m => m.metadata)
      };

      await onSubmit(submissionData);

      // Clear current draft after successful submission
      clearCurrentDraft();
      clearMedia();
      setHasUnsavedChanges(false);

      toast({
        title: 'Success',
        description: 'Portfolio item saved successfully!'
      });

    } catch (error) {
      console.error('Error submitting portfolio:', error);
      toast({
        title: 'Error',
        description: 'Failed to save portfolio item. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setShowUnsavedDialog(true);
    } else {
      onCancel?.();
    }
  };

  const handleDiscardChanges = () => {
    setHasUnsavedChanges(false);
    setShowUnsavedDialog(false);
    clearCurrentDraft();
    clearMedia();
    onCancel?.();
  };

  const handleSaveAndExit = async () => {
    try {
      await saveDraft({
        ...watchedValues,
        tools_used: selectedTools,
        tagged_toolkits: selectedToolkits,
        media_urls: uploadedMedia.map(m => m.url),
        storage_metadata: {
          file_paths: uploadedMedia.map(m => m.metadata?.fileName || ''),
          file_sizes: uploadedMedia.map(m => m.metadata?.size || 0),
          upload_timestamps: uploadedMedia.map(() => new Date().toISOString()),
          livepeer_asset_id: uploadedMedia.find(m => m.type === 'video')?.metadata?.playbackId
        }
      });

      setHasUnsavedChanges(false);
      setShowUnsavedDialog(false);

      toast({
        title: 'Draft saved',
        description: 'Your progress has been saved as a draft'
      });

      onCancel?.();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save draft',
        variant: 'destructive'
      });
    }
  };

  const handleSaveDraft = async () => {
    try {
      await saveDraft({
        ...watchedValues,
        tagged_toolkits: selectedToolkits,
        media_urls: uploadedMedia.map(m => m.url),
        storage_metadata: {
          file_paths: uploadedMedia.map(m => m.metadata?.fileName || ''),
          file_sizes: uploadedMedia.map(m => m.metadata?.size || 0),
          upload_timestamps: uploadedMedia.map(() => new Date().toISOString()),
          livepeer_asset_id: uploadedMedia.find(m => m.type === 'video')?.metadata?.playbackId
        }
      });

      toast({
        title: 'Draft saved',
        description: 'Your progress has been saved as a draft'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save draft',
        variant: 'destructive'
      });
    }
  };

  const handleDraftSelect = (draft: any) => {
    // Load draft data into form
    setValue('title', draft.title);
    setValue('description', draft.description);
    setValue('tools_used', draft.tools_used);
    setValue('project_url', draft.project_url);
    setValue('is_public', draft.is_public);
    setValue('status', draft.status);
    setValue('content_type', draft.content_type);
    setSelectedToolkits(draft.tagged_toolkits || []);

    // Load media if available
    if (draft.media_urls && draft.media_urls.length > 0) {
      clearMedia();
      draft.media_urls.forEach((url: string, index: number) => {
        addMedia({
          url,
          type: draft.media_type || 'image',
          metadata: draft.storage_metadata ? {
            fileName: draft.storage_metadata.file_paths[index],
            size: draft.storage_metadata.file_sizes[index]
          } : undefined
        });
      });
    }

    setActiveTab('form');
    toast({
      title: 'Draft loaded',
      description: 'Draft has been loaded into the form'
    });
  };

  const handleDraftDelete = (draftId: string) => {
    toast({
      title: 'Draft deleted',
      description: 'Draft has been deleted successfully'
    });
  };

  const handleDraftPublish = (portfolioId: string) => {
    toast({
      title: 'Draft published',
      description: 'Your portfolio item is now live!'
    });
    // Optionally redirect or refresh
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6 bg-gray-900 text-white">
      {/* Auto-save status indicator */}
      {autoSaveStatus !== 'idle' && (
        <div className="flex items-center gap-2 text-sm">
          {autoSaveStatus === 'saving' && (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Saving draft...</span>
            </>
          )}
          {autoSaveStatus === 'saved' && (
            <>
              <Save className="w-4 h-4 text-green-500" />
              <span className="text-green-500">Draft saved</span>
            </>
          )}
          {autoSaveStatus === 'error' && (
            <>
              <FileText className="w-4 h-4 text-red-500" />
              <span className="text-red-500">Save failed</span>
            </>
          )}
        </div>
      )}

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'form' | 'drafts')}>
        <TabsList className="grid w-full grid-cols-2 bg-gray-800 border-gray-600">
          <TabsTrigger value="form" className="flex items-center gap-2 data-[state=active]:bg-gray-700 data-[state=active]:text-white text-gray-300">
            <FileText className="w-4 h-4" />
            Portfolio Form
          </TabsTrigger>
          <TabsTrigger value="drafts" className="flex items-center gap-2 data-[state=active]:bg-gray-700 data-[state=active]:text-white text-gray-300">
            <Archive className="w-4 h-4" />
            Drafts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="form" className="space-y-6">
          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Media Upload Section */}
            <div className="space-y-4">
              <Label className="text-lg font-semibold">Media Upload</Label>
              <PortfolioMediaUploader
                userId={user.id}
                onMediaUpload={addMedia}
                onError={handleMediaError}
                className="w-full"
              />

              {uploadedMedia.length > 0 && (
                <MediaPreview
                  media={uploadedMedia}
                  onRemove={removeMedia}
                  className="mt-4"
                />
              )}
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-white">Title *</Label>
                <Input
                  id="title"
                  {...register('title')}
                  placeholder="Enter project title"
                  className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
                {errors.title && (
                  <p className="text-sm text-red-500">{errors.title.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="project_url" className="text-white">Project URL</Label>
                <Input
                  id="project_url"
                  {...register('project_url')}
                  placeholder="https://your-project.com"
                  className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-white">Description *</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Describe your project..."
                rows={4}
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 resize-none"
              />
              {errors.description && (
                <p className="text-sm text-red-500">{errors.description.message}</p>
              )}
            </div>

            {/* Tools and Toolkits */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Tools Used *</Label>
                <div className="bg-gray-800 border border-gray-600 rounded-md p-3 min-h-[120px] max-h-[200px] overflow-y-auto">
                  {userTools.length === 0 ? (
                    <div className="text-gray-400 text-center py-4">
                      <p>No tools found in your library.</p>
                      <p className="text-sm">Add tools to your library first to select them here.</p>
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 gap-2">
                      {userTools.map((tool) => (
                        <label
                          key={tool.id}
                          className="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-700 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={selectedTools.includes(tool.name)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedTools([...selectedTools, tool.name]);
                              } else {
                                setSelectedTools(selectedTools.filter(t => t !== tool.name));
                              }
                            }}
                            className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                          />
                          <div className="flex items-center space-x-2">
                            {tool.logo_url && (
                              <img
                                src={tool.logo_url}
                                alt={tool.name}
                                className="w-6 h-6 rounded"
                              />
                            )}
                            <span className="text-white">{tool.name}</span>
                          </div>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
                {selectedTools.length === 0 && (
                  <p className="text-sm text-red-500">Please select at least one tool</p>
                )}
                <p className="text-xs text-gray-400">
                  Selected: {selectedTools.length} tool{selectedTools.length !== 1 ? 's' : ''}
                </p>
              </div>

              <ToolkitSelector
                selectedToolkits={selectedToolkits}
                onSelectionChange={setSelectedToolkits}
                maxSelection={5}
              />
            </div>

            {/* Settings */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status" className="text-white">Status</Label>
                <select
                  id="status"
                  {...register('status')}
                  className="w-full p-2 bg-gray-800 border border-gray-600 text-white rounded-md focus:border-blue-500"
                >
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content_type" className="text-white">Content Type</Label>
                <select
                  id="content_type"
                  {...register('content_type')}
                  className="w-full p-2 bg-gray-800 border border-gray-600 text-white rounded-md focus:border-blue-500"
                >
                  <option value="post">Post Only</option>
                  <option value="reel">Reel Only</option>
                  <option value="both">Both Post and Reel</option>
                </select>
              </div>

              <div className="flex items-center space-x-2 pt-6">
                <input
                  type="checkbox"
                  id="is_public"
                  {...register('is_public')}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
                <Label htmlFor="is_public" className="text-white">Make public</Label>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleSaveDraft}
                  disabled={isSubmitting}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save Draft
                </Button>
              </div>

              <div className="flex gap-2">
                {onCancel && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                    className="border-gray-600 text-gray-300 hover:bg-gray-700"
                  >
                    Cancel
                  </Button>
                )}
                <Button
                  type="submit"
                  disabled={isSubmitting || isLoading || uploadedMedia.length === 0 || selectedTools.length === 0}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Portfolio Item'
                  )}
                </Button>
              </div>
            </div>
          </form>
        </TabsContent>

        <TabsContent value="drafts">
          <DraftManager
            userId={user.id}
            onDraftSelect={handleDraftSelect}
            onDraftDelete={handleDraftDelete}
            onDraftPublish={handleDraftPublish}
          />
        </TabsContent>
      </Tabs>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showUnsavedDialog} onOpenChange={setShowUnsavedDialog}>
        <AlertDialogContent className="bg-gray-800 border-gray-600">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-white flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
              Unsaved Changes
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-300">
              You have unsaved changes in your portfolio item. What would you like to do?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="gap-2">
            <AlertDialogCancel
              onClick={handleDiscardChanges}
              className="bg-red-600 hover:bg-red-700 text-white border-red-600"
            >
              <X className="w-4 h-4 mr-2" />
              Discard Changes
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSaveAndExit}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              Save as Draft
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
