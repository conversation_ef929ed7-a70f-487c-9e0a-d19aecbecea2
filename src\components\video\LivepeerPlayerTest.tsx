import React, { useEffect, useState, useRef } from 'react';
import { LivepeerPlayer } from './LivepeerPlayer';
import * as Player from "@livepeer/react/player";
import { getLivepeerSrc } from '@/lib/livepeer';
import Hls from 'hls.js';

export function LivepeerPlayerTest() {
    // Test with multiple known playback IDs from your portfolio
    const testPlaybackIds = [
        "35791ht4d64opfwb", // Original test ID
        "6072p6livmjj455w", // From your portfolio
        "b37dgwuagxkabin0", // From your portfolio
        "ca17t17v45i6h58q", // From your portfolio
        "af35gt2u0vyukuk0"  // From your portfolio
    ];
    const testPlaybackId = testPlaybackIds[1]; // Use "6072p6livmjj455w" from your portfolio
    const testHlsUrl = `https://livepeercdn.com/hls/${testPlaybackId}/index.m3u8`; // Use the working URL format

    const [getSrcResult, setGetSrcResult] = useState<any>(null);
    const [urlTestResults, setUrlTestResults] = useState<any>({});
    const videoRef = useRef<HTMLVideoElement>(null);
    const [hlsStatus, setHlsStatus] = useState<string>('Initializing...');

    useEffect(() => {
        // Test getLivepeerSrc function
        try {
            const result = getLivepeerSrc(testPlaybackId);
            console.log('getLivepeerSrc result:', result);
            setGetSrcResult(result);

            // Test each URL accessibility
            if (result && Array.isArray(result)) {
                result.forEach(async (source, index) => {
                    try {
                        const response = await fetch(source.src, { method: 'HEAD' });
                        setUrlTestResults(prev => ({
                            ...prev,
                            [index]: { status: response.status, ok: response.ok, url: source.src }
                        }));
                    } catch (error) {
                        setUrlTestResults(prev => ({
                            ...prev,
                            [index]: { error: error.message, url: source.src }
                        }));
                    }
                });
            }
        } catch (error) {
            console.error('getLivepeerSrc error:', error);
            setGetSrcResult({ error: error.message });
        }
    }, [testPlaybackId]);

    // HLS.js setup for Test 6
    useEffect(() => {
        if (videoRef.current) {
            const video = videoRef.current;

            if (Hls.isSupported()) {
                setHlsStatus('HLS.js supported, loading stream...');
                const hls = new Hls({
                    debug: true,
                    enableWorker: false,
                });

                hls.loadSource(testHlsUrl);
                hls.attachMedia(video);

                hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    setHlsStatus('✅ HLS manifest loaded successfully!');
                    console.log('HLS manifest parsed, ready to play');
                });

                hls.on(Hls.Events.ERROR, (event, data) => {
                    setHlsStatus(`❌ HLS Error: ${data.type} - ${data.details}`);
                    console.error('HLS error:', data);
                });

                return () => {
                    hls.destroy();
                };
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                setHlsStatus('✅ Native HLS support detected');
                video.src = testHlsUrl;
            } else {
                setHlsStatus('❌ HLS not supported in this browser');
            }
        }
    }, [testHlsUrl]);

    return (
        <div className="p-8 space-y-8">
            <h1 className="text-2xl font-bold text-white">Livepeer Player Test</h1>

            {/* Test 1: Using playback ID */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 1: Using Playback ID</h2>
                <div className="w-full max-w-2xl aspect-video">
                    <LivepeerPlayer
                        playbackId={testPlaybackId}
                        title="Test Video - Playback ID"
                        muted={false}
                        autoPlay={false}
                    />
                </div>
            </div>

            {/* Test 2: Using HLS URL */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 2: Using HLS URL</h2>
                <div className="w-full max-w-2xl aspect-video">
                    <LivepeerPlayer
                        src={testHlsUrl}
                        title="Test Video - HLS URL"
                        muted={false}
                        autoPlay={false}
                    />
                </div>
            </div>

            {/* Test 3: Using old format URL */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 3: Using Old Format URL</h2>
                <div className="w-full max-w-2xl aspect-video">
                    <LivepeerPlayer
                        src="https://vod-cdn.lp-playback.studio/raw/35791ht4d64opfwb/catalyst-vod-com/hls/index.m3u8"
                        title="Test Video - Old Format"
                        muted={false}
                        autoPlay={false}
                    />
                </div>
            </div>

            {/* Test 4: Simple Livepeer Player */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 4: Simple Livepeer Player</h2>
                <div className="w-full max-w-2xl aspect-video">
                    <Player.Root src={[{ src: testHlsUrl, type: "application/vnd.apple.mpegurl" }]}>
                        <Player.Container className="h-full w-full overflow-hidden bg-black rounded-lg">
                            <Player.Video
                                title="Simple Test Video"
                                className="h-full w-full object-cover"
                            />
                            <Player.Controls className="bg-gradient-to-b from-transparent to-black/60 p-4">
                                <Player.PlayPauseTrigger className="text-white">
                                    Play/Pause
                                </Player.PlayPauseTrigger>
                            </Player.Controls>
                        </Player.Container>
                    </Player.Root>
                </div>
            </div>

            {/* Test 5: HTML5 Video (Direct Test) */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 5: HTML5 Video (Direct)</h2>
                <div className="w-full max-w-2xl aspect-video">
                    <video
                        className="w-full h-full bg-black rounded-lg"
                        controls
                        preload="metadata"
                        crossOrigin="anonymous"
                    >
                        <source src={testHlsUrl} type="application/vnd.apple.mpegurl" />
                        <p className="text-white">Your browser doesn't support HLS video.</p>
                    </video>
                </div>
            </div>

            {/* Test 6: HLS.js Video Player */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 6: HLS.js Player</h2>
                <p className="text-sm text-gray-300">Status: {hlsStatus}</p>
                <div className="w-full max-w-2xl aspect-video">
                    <video
                        ref={videoRef}
                        className="w-full h-full bg-black rounded-lg"
                        controls
                        preload="metadata"
                    />
                </div>
            </div>

            {/* Test 7: Portfolio URL Format */}
            <div className="space-y-4">
                <h2 className="text-lg font-semibold text-white">Test 7: Portfolio URL Format</h2>
                <p className="text-sm text-gray-300">Using: https://vod-cdn.lp-playback.studio/raw/af35gt2u0vyukuk0/catalyst-vod-com/hls/index.m3u8</p>
                <div className="w-full max-w-2xl aspect-video">
                    <video
                        className="w-full h-full bg-black rounded-lg"
                        controls
                        preload="metadata"
                        crossOrigin="anonymous"
                    >
                        <source src="https://vod-cdn.lp-playback.studio/raw/af35gt2u0vyukuk0/catalyst-vod-com/hls/index.m3u8" type="application/vnd.apple.mpegurl" />
                        <p className="text-white">Your browser doesn't support HLS video.</p>
                    </video>
                </div>
            </div>

            {/* Debug Info */}
            <div className="space-y-2 text-sm text-gray-300">
                <h3 className="text-white font-semibold">Debug Info:</h3>
                <p>Playback ID: {testPlaybackId}</p>
                <p>HLS URL: {testHlsUrl}</p>
                <p>getLivepeerSrc Result: {JSON.stringify(getSrcResult, null, 2)}</p>

                <h4 className="text-white font-semibold mt-4">URL Accessibility Test:</h4>
                {Object.entries(urlTestResults).map(([index, result]: [string, any]) => (
                    <div key={index} className="text-xs">
                        <p>URL {parseInt(index) + 1}: {result.ok ? '✅ Accessible' : '❌ Failed'}</p>
                        <p className="text-gray-400 truncate">{result.url}</p>
                        {result.error && <p className="text-red-400">Error: {result.error}</p>}
                        {result.status && <p className="text-blue-400">Status: {result.status}</p>}
                    </div>
                ))}

                <p className="mt-4">Check browser console for any errors</p>
                <p>If videos don't load, check Network tab for CORS errors</p>
            </div>
        </div>
    );
}
