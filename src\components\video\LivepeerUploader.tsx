import { useState, useRef, useEffect } from 'react';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Card } from '../ui/card';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { storageCleanupService } from '@/services/storageCleanupService';
import { AlertCircle, CheckCircle, Clock, Trash2 } from 'lucide-react';

// Enhanced draft interface for saving upload progress and metadata
interface UploadDraft {
    fileName: string;
    fileSize: number;
    lastProgress: number;
    assetId?: string;
    uploadUrl?: string;
    playbackId?: string;
    title?: string;
    description?: string;
    tags?: string[];
    timestamp: number;
    userId?: string;
    attempts: number;
    lastError?: string;
}

// Video metadata interface
interface VideoMetadata {
    title: string;
    description: string;
    tags: string[];
    playbackId: string;
    playbackUrl: string;
}

// Props interface for the LivepeerUploader component
interface LivepeerUploaderProps {
    onUploadComplete: (url: string, metadata?: VideoMetadata) => void;
    onError: (error: string) => void;
    userId?: string; // Add userId for better draft management
    maxSizeInMB?: number; // Add size limit
}

export function LivepeerUploader({
    onUploadComplete,
    onError,
    userId,
    maxSizeInMB = 500
}: LivepeerUploaderProps): JSX.Element {
    const [uploading, setUploading] = useState(false);
    const [progress, setProgress] = useState(0);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [draft, setDraft] = useState<UploadDraft | null>(null);
    const [title, setTitle] = useState('');
    const [description, setDescription] = useState('');
    const [tags, setTags] = useState('');
    const [uploadStatus, setUploadStatus] = useState<'idle' | 'uploading' | 'processing' | 'complete' | 'error'>('idle');
    const inputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();

    // Load draft on component mount
    useEffect(() => {
        const draftKey = userId ? `videoUploadDraft_${userId}` : 'videoUploadDraft';
        const savedDraft = localStorage.getItem(draftKey);
        if (savedDraft) {
            try {
                const parsedDraft = JSON.parse(savedDraft);
                // Only load drafts that are less than 24 hours old
                if (Date.now() - parsedDraft.timestamp < 24 * 60 * 60 * 1000) {
                    setDraft(parsedDraft);
                    // Restore form data from draft
                    if (parsedDraft.title) setTitle(parsedDraft.title);
                    if (parsedDraft.description) setDescription(parsedDraft.description);
                    if (parsedDraft.tags) setTags(parsedDraft.tags.join(', '));

                    toast({
                        title: 'Draft restored',
                        description: `Restored draft for ${parsedDraft.fileName}`,
                    });
                } else {
                    localStorage.removeItem(draftKey);
                }
            } catch (e) {
                console.error('Error loading draft:', e);
                localStorage.removeItem(draftKey);
            }
        }
    }, [userId, toast]);

    // Save draft whenever progress updates or form data changes
    const saveDraft = (draftData: Partial<UploadDraft>) => {
        if (!selectedFile) return;

        const newDraft: UploadDraft = {
            fileName: selectedFile.name,
            fileSize: selectedFile.size,
            lastProgress: progress,
            timestamp: Date.now(),
            title,
            description,
            tags: tags.split(',').map(t => t.trim()).filter(t => t),
            userId,
            attempts: (draft?.attempts || 0) + (draftData.assetId ? 1 : 0),
            ...draftData
        };

        const draftKey = userId ? `videoUploadDraft_${userId}` : 'videoUploadDraft';
        localStorage.setItem(draftKey, JSON.stringify(newDraft));
        setDraft(newDraft);
    };

    // Clear draft when upload completes or fails
    const clearDraft = async () => {
        const draftKey = userId ? `videoUploadDraft_${userId}` : 'videoUploadDraft';
        localStorage.removeItem(draftKey);

        // If there was an incomplete upload, schedule cleanup
        if (draft?.assetId && draft?.uploadUrl && userId) {
            try {
                // Schedule Livepeer asset cleanup (this would need Livepeer API integration)
                console.log(`Scheduling cleanup for Livepeer asset: ${draft.assetId}`);
                // In a real implementation, you'd call Livepeer API to delete the asset
            } catch (error) {
                console.error('Error scheduling Livepeer cleanup:', error);
            }
        }

        setDraft(null);
        setTitle('');
        setDescription('');
        setTags('');
        setUploadStatus('idle');
    };

    const validateFile = (file: File): string | null => {
        if (!file.type.includes('video/')) {
            return 'Please upload a video file';
        }

        const maxSizeBytes = maxSizeInMB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            return `File size too large. Maximum size: ${maxSizeInMB}MB`;
        }

        // Check for supported video formats
        const supportedFormats = ['video/mp4', 'video/webm', 'video/quicktime', 'video/x-msvideo'];
        if (!supportedFormats.includes(file.type)) {
            return 'Unsupported video format. Please use MP4, WebM, MOV, or AVI';
        }

        return null;
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        const validationError = validateFile(file);
        if (validationError) {
            onError(validationError);
            return;
        }

        setSelectedFile(file);
        setUploadStatus('idle');
        // Clear existing draft when new file is selected
        clearDraft();
    };

    // Function to check if an asset is ready for playback
    const checkPlaybackAvailability = async (assetId: string): Promise<boolean> => {
        const apiKey = import.meta.env.VITE_LIVEPEER_API_KEY;

        try {
            // Check asset status through Livepeer API
            const response = await fetch(`https://livepeer.studio/api/asset/${assetId}`, {
                headers:
                {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                // Check if the asset status is ready and has valid playback URLs
                if (data.status?.phase === 'ready' && data.playbackId) {
                    return true;
                }
            }
            return false;
        } catch (error) {
            console.warn('Error checking playback availability:', error);
            return false;
        }
    }; const generatePlaybackUrl = (playbackId: string): string => {
        // Use the official Livepeer playback URL format
        return `https://livepeercdn.com/hls/${playbackId}/index.m3u8`;
    };

    const handleUpload = async () => {
        if (!selectedFile) return;

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 900000); // 15 min timeout
        const apiKey = import.meta.env.VITE_LIVEPEER_API_KEY;

        try {
            setUploading(true);
            setProgress(10);

            // Step 1: Request upload URL (or use existing one from draft)
            let uploadUrl: string;
            let asset: { id: string };

            if (draft?.uploadUrl && draft?.assetId) {
                uploadUrl = draft.uploadUrl;
                asset = { id: draft.assetId };
                console.log('Resuming upload from draft');
            } else {
                const createAssetResponse = await fetch('https://livepeer.studio/api/asset/request-upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: selectedFile.name
                    })
                });

                if (!createAssetResponse.ok) {
                    throw new Error(`Failed to request upload URL: ${createAssetResponse.status} ${createAssetResponse.statusText}`);
                }

                const responseData = await createAssetResponse.json();
                if (!responseData.url || !responseData.asset) {
                    throw new Error('Invalid response from Livepeer API');
                }

                uploadUrl = responseData.url;
                asset = responseData.asset;

                // Save draft with upload URL and asset ID
                saveDraft({
                    uploadUrl,
                    assetId: asset.id
                });
            }

            setProgress(20);

            // Step 2: Upload the file
            const formData = new FormData();
            formData.append('file', selectedFile);

            const uploadResponse = await fetch(uploadUrl, {
                method: 'PUT',
                body: selectedFile,
                headers: {
                    'Content-Type': selectedFile.type
                },
                signal: controller.signal
            });

            if (!uploadResponse.ok) {
                throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
            }

            setProgress(80);

            // Step 3: Poll for processing status
            let retries = 0;
            const maxRetries = 30;
            while (retries < maxRetries) {
                const statusResponse = await fetch(`https://livepeer.studio/api/asset/${asset.id}`, {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    }
                });

                if (!statusResponse.ok) {
                    throw new Error(`Failed to check processing status: ${statusResponse.status}`);
                }

                const statusData = await statusResponse.json();
                if (statusData.status?.phase === 'ready' && statusData.playbackId) {
                    // Generate the full playback URL
                    const playbackUrl = generatePlaybackUrl(statusData.playbackId);

                    // Success! Return the URL and metadata
                    clearTimeout(timeoutId);
                    clearDraft();
                    setProgress(100);

                    // Pass both playback ID and full URL in the metadata
                    onUploadComplete(playbackUrl, {
                        title,
                        description,
                        tags: tags.split(',').map(t => t.trim()).filter(t => t),
                        playbackId: statusData.playbackId,
                        playbackUrl
                    });
                    return;
                }

                retries++;
                await new Promise(resolve => setTimeout(resolve, 2000));
                setProgress(80 + Math.min(19, (retries / maxRetries) * 20));
            }

            throw new Error('Processing timed out');

        } catch (error) {
            console.error('Upload error:', error);
            onError(error instanceof Error ? error.message : 'Upload failed');
        } finally {
            setUploading(false);
            controller.abort();
        }
    };

    return (
        <Card className="p-4 w-full">
            <div className="space-y-4">
                <input
                    type="file"
                    accept="video/*"
                    onChange={handleFileSelect}
                    className="hidden"
                    ref={inputRef}
                />

                {draft && (
                    <div className="text-sm text-gray-500">
                        Draft available for: {draft.fileName} ({Math.round(draft.lastProgress)}% complete)
                    </div>
                )}

                <Button
                    onClick={() => inputRef.current?.click()}
                    disabled={uploading}
                    variant="outline"
                    className="w-full"
                >
                    {selectedFile ? selectedFile.name : 'Select Video'}
                </Button>

                {/* Project details form */}
                <div className="space-y-4">
                    <Input
                        placeholder="Video Title"
                        value={title}
                        onChange={(e) => {
                            setTitle(e.target.value);
                            saveDraft({ title: e.target.value });
                        }}
                        disabled={uploading}
                    />

                    <Textarea
                        placeholder="Video Description"
                        value={description}
                        onChange={(e) => {
                            setDescription(e.target.value);
                            saveDraft({ description: e.target.value });
                        }}
                        disabled={uploading}
                    />

                    <Input
                        placeholder="Tags (comma-separated)"
                        value={tags}
                        onChange={(e) => {
                            setTags(e.target.value);
                            saveDraft({ tags: e.target.value.split(',').map(t => t.trim()).filter(t => t) });
                        }}
                        disabled={uploading}
                    />
                </div>

                {(uploading || progress > 0) && (
                    <Progress value={progress} className="w-full" />
                )}

                <Button
                    onClick={handleUpload}
                    disabled={!selectedFile || uploading}
                    className="w-full"
                >
                    {uploading ? 'Uploading...' : 'Upload Video'}
                </Button>
            </div>
        </Card>
    );
}
