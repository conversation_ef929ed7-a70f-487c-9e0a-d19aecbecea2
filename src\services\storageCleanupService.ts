import { 
    collection, 
    doc, 
    addDoc, 
    deleteDoc, 
    getDocs, 
    query, 
    where, 
    orderBy, 
    limit,
    Timestamp,
    writeBatch
} from 'firebase/firestore';
import { ref, deleteObject, listAll } from 'firebase/storage';
import { db, storage } from '@/lib/firebase';

// Interface for cleanup queue items
interface CleanupQueueItem {
    id?: string;
    filePath: string;
    fileUrl: string;
    userId: string;
    reason: 'draft_abandoned' | 'item_deleted' | 'orphaned' | 'archive_expired';
    scheduledFor: Timestamp;
    createdAt: Timestamp;
    attempts: number;
    lastAttempt?: Timestamp;
    error?: string;
}

// Interface for storage usage tracking
interface StorageUsage {
    userId: string;
    totalSize: number;
    fileCount: number;
    lastUpdated: Timestamp;
    breakdown: {
        portfolio: number;
        drafts: number;
        archives: number;
    };
}

export class StorageCleanupService {
    private static instance: StorageCleanupService;
    private cleanupQueue = collection(db, 'storage_cleanup_queue');
    private storageUsage = collection(db, 'storage_usage');

    static getInstance(): StorageCleanupService {
        if (!StorageCleanupService.instance) {
            StorageCleanupService.instance = new StorageCleanupService();
        }
        return StorageCleanupService.instance;
    }

    /**
     * Schedule a file for cleanup
     */
    async scheduleCleanup(
        filePath: string,
        fileUrl: string,
        userId: string,
        reason: CleanupQueueItem['reason'],
        delayInHours: number = 24
    ): Promise<void> {
        try {
            const scheduledFor = new Date();
            scheduledFor.setHours(scheduledFor.getHours() + delayInHours);

            const cleanupItem: Omit<CleanupQueueItem, 'id'> = {
                filePath,
                fileUrl,
                userId,
                reason,
                scheduledFor: Timestamp.fromDate(scheduledFor),
                createdAt: Timestamp.now(),
                attempts: 0
            };

            await addDoc(this.cleanupQueue, cleanupItem);
            console.log(`Scheduled cleanup for ${filePath} in ${delayInHours} hours`);
        } catch (error) {
            console.error('Error scheduling cleanup:', error);
        }
    }

    /**
     * Cancel scheduled cleanup for a file
     */
    async cancelCleanup(filePath: string): Promise<void> {
        try {
            const q = query(
                this.cleanupQueue,
                where('filePath', '==', filePath)
            );
            const querySnapshot = await getDocs(q);
            
            const batch = writeBatch(db);
            querySnapshot.docs.forEach(doc => {
                batch.delete(doc.ref);
            });
            
            await batch.commit();
            console.log(`Cancelled cleanup for ${filePath}`);
        } catch (error) {
            console.error('Error cancelling cleanup:', error);
        }
    }

    /**
     * Process pending cleanup items
     */
    async processCleanupQueue(): Promise<void> {
        try {
            const now = Timestamp.now();
            const q = query(
                this.cleanupQueue,
                where('scheduledFor', '<=', now),
                orderBy('scheduledFor'),
                limit(50) // Process in batches
            );

            const querySnapshot = await getDocs(q);
            const batch = writeBatch(db);

            for (const docSnapshot of querySnapshot.docs) {
                const item = { id: docSnapshot.id, ...docSnapshot.data() } as CleanupQueueItem;
                
                try {
                    // Attempt to delete the file
                    const fileRef = ref(storage, item.filePath);
                    await deleteObject(fileRef);
                    
                    // Remove from cleanup queue
                    batch.delete(docSnapshot.ref);
                    console.log(`Successfully cleaned up ${item.filePath}`);
                    
                } catch (deleteError) {
                    console.error(`Failed to delete ${item.filePath}:`, deleteError);
                    
                    // Update attempts and error
                    const updatedItem = {
                        ...item,
                        attempts: item.attempts + 1,
                        lastAttempt: Timestamp.now(),
                        error: deleteError instanceof Error ? deleteError.message : 'Unknown error'
                    };

                    // If too many attempts, remove from queue
                    if (updatedItem.attempts >= 3) {
                        batch.delete(docSnapshot.ref);
                        console.log(`Giving up on ${item.filePath} after 3 attempts`);
                    } else {
                        // Reschedule for later
                        const newScheduledFor = new Date();
                        newScheduledFor.setHours(newScheduledFor.getHours() + 1);
                        updatedItem.scheduledFor = Timestamp.fromDate(newScheduledFor);
                        
                        batch.update(docSnapshot.ref, updatedItem);
                    }
                }
            }

            await batch.commit();
        } catch (error) {
            console.error('Error processing cleanup queue:', error);
        }
    }

    /**
     * Clean up abandoned drafts
     */
    async cleanupAbandonedDrafts(): Promise<void> {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 7); // 7 days old

            const draftsQuery = query(
                collection(db, 'portfolio_drafts'),
                where('updated_at', '<', cutoffDate.toISOString())
            );

            const draftsSnapshot = await getDocs(draftsQuery);
            const batch = writeBatch(db);

            for (const draftDoc of draftsSnapshot.docs) {
                const draft = draftDoc.data();
                
                // Schedule media files for cleanup
                if (draft.media_url) {
                    await this.scheduleCleanup(
                        this.extractPathFromUrl(draft.media_url),
                        draft.media_url,
                        draft.userId,
                        'draft_abandoned',
                        0 // Immediate cleanup for old drafts
                    );
                }

                if (draft.media_urls && Array.isArray(draft.media_urls)) {
                    for (const url of draft.media_urls) {
                        await this.scheduleCleanup(
                            this.extractPathFromUrl(url),
                            url,
                            draft.userId,
                            'draft_abandoned',
                            0
                        );
                    }
                }

                // Delete the draft document
                batch.delete(draftDoc.ref);
            }

            await batch.commit();
            console.log(`Cleaned up ${draftsSnapshot.docs.length} abandoned drafts`);
        } catch (error) {
            console.error('Error cleaning up abandoned drafts:', error);
        }
    }

    /**
     * Clean up expired archives
     */
    async cleanupExpiredArchives(): Promise<void> {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 90); // 90 days old

            const archivesQuery = query(
                collection(db, 'portfolio_archives'),
                where('archived_at', '<', cutoffDate.toISOString())
            );

            const archivesSnapshot = await getDocs(archivesQuery);
            const batch = writeBatch(db);

            for (const archiveDoc of archivesSnapshot.docs) {
                const archive = archiveDoc.data();
                
                // Schedule media files for cleanup
                if (archive.media_url) {
                    await this.scheduleCleanup(
                        this.extractPathFromUrl(archive.media_url),
                        archive.media_url,
                        archive.userId,
                        'archive_expired',
                        0
                    );
                }

                if (archive.media_urls && Array.isArray(archive.media_urls)) {
                    for (const url of archive.media_urls) {
                        await this.scheduleCleanup(
                            this.extractPathFromUrl(url),
                            url,
                            archive.userId,
                            'archive_expired',
                            0
                        );
                    }
                }

                // Delete the archive document
                batch.delete(archiveDoc.ref);
            }

            await batch.commit();
            console.log(`Cleaned up ${archivesSnapshot.docs.length} expired archives`);
        } catch (error) {
            console.error('Error cleaning up expired archives:', error);
        }
    }

    /**
     * Find and clean up orphaned files
     */
    async cleanupOrphanedFiles(userId: string): Promise<void> {
        try {
            // Get all files in user's portfolio directory
            const portfolioRef = ref(storage, `portfolio/${userId}`);
            const fileList = await listAll(portfolioRef);
            
            // Get all active portfolio items
            const portfolioQuery = query(
                collection(db, 'portfolio'),
                where('userId', '==', userId)
            );
            const portfolioSnapshot = await getDocs(portfolioQuery);
            
            // Get all draft items
            const draftsQuery = query(
                collection(db, 'portfolio_drafts'),
                where('userId', '==', userId)
            );
            const draftsSnapshot = await getDocs(draftsQuery);

            // Collect all referenced URLs
            const referencedUrls = new Set<string>();
            
            [...portfolioSnapshot.docs, ...draftsSnapshot.docs].forEach(doc => {
                const data = doc.data();
                if (data.media_url) referencedUrls.add(data.media_url);
                if (data.media_urls && Array.isArray(data.media_urls)) {
                    data.media_urls.forEach((url: string) => referencedUrls.add(url));
                }
            });

            // Check each file to see if it's referenced
            for (const fileRef of fileList.items) {
                const fileUrl = await fileRef.getDownloadURL();
                
                if (!referencedUrls.has(fileUrl)) {
                    // This file is orphaned, schedule for cleanup
                    await this.scheduleCleanup(
                        fileRef.fullPath,
                        fileUrl,
                        userId,
                        'orphaned',
                        24 // Give 24 hours in case of race conditions
                    );
                }
            }
        } catch (error) {
            console.error('Error cleaning up orphaned files:', error);
        }
    }

    /**
     * Update storage usage statistics
     */
    async updateStorageUsage(userId: string): Promise<void> {
        try {
            const portfolioRef = ref(storage, `portfolio/${userId}`);
            const draftsRef = ref(storage, `portfolio-drafts/${userId}`);
            const archivesRef = ref(storage, `portfolio-archives/${userId}`);

            const [portfolioList, draftsList, archivesList] = await Promise.all([
                listAll(portfolioRef).catch(() => ({ items: [] })),
                listAll(draftsRef).catch(() => ({ items: [] })),
                listAll(archivesRef).catch(() => ({ items: [] }))
            ]);

            let totalSize = 0;
            let portfolioSize = 0;
            let draftsSize = 0;
            let archivesSize = 0;

            // Calculate sizes (Note: Firebase Storage doesn't provide file sizes directly)
            // This is a simplified approach - in production, you'd track sizes in metadata
            const fileCount = portfolioList.items.length + draftsList.items.length + archivesList.items.length;

            const usage: Omit<StorageUsage, 'id'> = {
                userId,
                totalSize,
                fileCount,
                lastUpdated: Timestamp.now(),
                breakdown: {
                    portfolio: portfolioSize,
                    drafts: draftsSize,
                    archives: archivesSize
                }
            };

            // Update or create usage document
            const usageDoc = doc(this.storageUsage, userId);
            await usageDoc.set(usage);
        } catch (error) {
            console.error('Error updating storage usage:', error);
        }
    }

    /**
     * Extract storage path from download URL
     */
    private extractPathFromUrl(url: string): string {
        try {
            const urlObj = new URL(url);
            const pathMatch = urlObj.pathname.match(/\/o\/(.+?)\?/);
            return pathMatch ? decodeURIComponent(pathMatch[1]) : '';
        } catch {
            return '';
        }
    }

    /**
     * Run all cleanup tasks
     */
    async runCleanupTasks(): Promise<void> {
        console.log('Starting storage cleanup tasks...');
        
        await Promise.all([
            this.processCleanupQueue(),
            this.cleanupAbandonedDrafts(),
            this.cleanupExpiredArchives()
        ]);
        
        console.log('Storage cleanup tasks completed');
    }
}

// Export singleton instance
export const storageCleanupService = StorageCleanupService.getInstance();
