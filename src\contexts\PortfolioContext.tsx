import React, { createContext, useContext, useState, useEffect } from 'react';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { useAuth } from './AuthContext';
import { PortfolioItem } from '@/types';

interface PortfolioContextType {
  portfolioItems: PortfolioItem[];
  loading: boolean;
  error: string | null;
  refreshPortfolio: () => Promise<void>;
}

const PortfolioContext = createContext<PortfolioContextType>({
  portfolioItems: [],
  loading: false,
  error: null,
  refreshPortfolio: async () => { }
});

export const usePortfolio = () => useContext(PortfolioContext);

export const PortfolioProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  const fetchPortfolioItems = async () => {
    if (!user) {
      setPortfolioItems([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const portfolioRef = collection(db, 'portfolio');

      // Try both field names for backward compatibility
      let querySnapshot;
      try {
        // First try with 'userId' field (new format)
        const q1 = query(
          portfolioRef,
          where('userId', '==', user.uid),
          where('status', 'in', ['published', 'draft']), // Include published and draft items
          orderBy('created_at', 'desc')
        );
        querySnapshot = await getDocs(q1);
      } catch (error) {
        console.log('Trying alternative query with user_id field');
        // Fallback to 'user_id' field (old format)
        const q2 = query(
          portfolioRef,
          where('user_id', '==', user.uid),
          where('status', 'in', ['published', 'draft', 'active']), // Include various status values
          orderBy('created_at', 'desc')
        );
        querySnapshot = await getDocs(q2);
      }

      const items: PortfolioItem[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        items.push({
          id: doc.id,
          ...data
        } as PortfolioItem);
      });

      console.log('Fetched portfolio items from context:', items);
      setPortfolioItems(items);
    } catch (err) {
      console.error('Error fetching portfolio items:', err);
      setError('Failed to load portfolio items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPortfolioItems();
  }, [user]);

  const refreshPortfolio = async () => {
    await fetchPortfolioItems();
  };

  return (
    <PortfolioContext.Provider value={{ portfolioItems, loading, error, refreshPortfolio }}>
      {children}
    </PortfolioContext.Provider>
  );
};

export default PortfolioProvider;
